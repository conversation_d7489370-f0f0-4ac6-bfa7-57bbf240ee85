<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.thor.TimesCardCouponOrderMappingMapper">
  
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.thor.entity.TimesCardCouponOrderMappingDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="coupon_case_id" jdbcType="BIGINT" property="couponCaseId" />
    <result column="times_card_order_no" jdbcType="VARCHAR" property="timesCardOrderNo" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, coupon_case_id, times_card_order_no, deleted, create_time, update_time, 
    gmt_create, gmt_update
  </sql>
  
  <select id="selectByCouponCaseId" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from times_card_coupon_order_mapping
    where coupon_case_id = #{couponCaseId,jdbcType=BIGINT}
    and deleted = 0
  </select>
  
  <select id="selectByTimesCardOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from times_card_coupon_order_mapping
    where times_card_order_no = #{timesCardOrderNo,jdbcType=VARCHAR}
    and deleted = 0
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from times_card_coupon_order_mapping
    where id = #{id,jdbcType=BIGINT}
    and deleted = 0
  </select>
  
  <insert id="insert" parameterType="so.dian.huashan.common.mapper.thor.entity.TimesCardCouponOrderMappingDO">
    insert into times_card_coupon_order_mapping (coupon_case_id, times_card_order_no, 
      deleted, create_time, update_time, 
      gmt_create, gmt_update)
    values (#{couponCaseId,jdbcType=BIGINT}, #{timesCardOrderNo,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT})
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.thor.entity.TimesCardCouponOrderMappingDO">
    update times_card_coupon_order_mapping
    set coupon_case_id = #{couponCaseId,jdbcType=BIGINT},
      times_card_order_no = #{timesCardOrderNo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    update times_card_coupon_order_mapping set deleted = 1
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
</mapper>
