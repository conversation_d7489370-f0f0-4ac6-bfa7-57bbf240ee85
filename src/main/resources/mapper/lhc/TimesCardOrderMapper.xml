<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.TimesCardOrderMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO">
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="loan_order_no" jdbcType="VARCHAR" property="loanOrderNo" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="pay_amount" jdbcType="BIGINT" property="payAmount" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="trade_no" jdbcType="VARCHAR" property="tradeNo" />
    <result column="refund_amount" jdbcType="BIGINT" property="refundAmount" />
    <result column="refund_time" jdbcType="TIMESTAMP" property="refundTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="profit_sharding" jdbcType="INTEGER" property="profitSharding" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  
  <sql id="Base_Column_List">
    order_no, loan_order_no, shop_id, user_id, order_amount, pay_amount, pay_time, 
    pay_type, status, trade_no, refund_amount, refund_time, end_time, profit_sharding, 
    create_time
  </sql>
  
  <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from times_card_order
    where order_no = #{orderNo,jdbcType=VARCHAR}
    and deleted = 0
  </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from times_card_order
    where id = #{id,jdbcType=BIGINT}
    and deleted = 0
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    update times_card_order set deleted = 1
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO">
    insert into times_card_order (order_no, loan_order_no, shop_id, 
      user_id, order_amount, pay_amount, 
      pay_time, pay_type, status, 
      trade_no, refund_amount, refund_time, 
      end_time, profit_sharding, create_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{loanOrderNo,jdbcType=VARCHAR}, #{shopId,jdbcType=BIGINT}, 
      #{userId,jdbcType=BIGINT}, #{orderAmount,jdbcType=BIGINT}, #{payAmount,jdbcType=BIGINT}, 
      #{payTime,jdbcType=TIMESTAMP}, #{payType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, 
      #{tradeNo,jdbcType=VARCHAR}, #{refundAmount,jdbcType=BIGINT}, #{refundTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP}, #{profitSharding,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO">
    update times_card_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      loan_order_no = #{loanOrderNo,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=BIGINT},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      pay_amount = #{payAmount,jdbcType=BIGINT},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      pay_type = #{payType,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      trade_no = #{tradeNo,jdbcType=VARCHAR},
      refund_amount = #{refundAmount,jdbcType=BIGINT},
      refund_time = #{refundTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      profit_sharding = #{profitSharding,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
