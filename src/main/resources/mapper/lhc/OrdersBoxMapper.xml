<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.lhc.OrdersBoxMapper">

  <select id="selectByOrderNos" parameterType="list" resultType="so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO">
    select
        order_no,
        pay_type,
        order_amount,
        device_type,
        loan_shop_id,
        return_time,
        loan_price_info,
        power_bank_no,
        loan_box_no
    from orders_box where order_no in
    <foreach collection="orderNos" item="orderNo" open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
  </select>

  <select id="selectByOrderNo" parameterType="java.lang.String" resultType="so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO">
    select
        order_no,
        pay_type,
        order_amount,
        device_type,
        loan_shop_id,
        return_time,
        loan_price_info,
        power_bank_no,
        loan_box_no,
        create_time
    from orders_box
    where order_no = #{orderNo,jdbcType=VARCHAR}
    and deleted = 0
  </select>

</mapper>