<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="so.dian.huashan.common.mapper.contract.ContractFrontDivideMapper">
  <resultMap id="BaseResultMap" type="so.dian.huashan.common.mapper.contract.dos.ContractFrontDivideDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contract_id" jdbcType="BIGINT" property="contractId" />
    <result column="party_a_id" jdbcType="BIGINT" property="partyAId" />
    <result column="brand_contract_id" jdbcType="BIGINT" property="brandContractId" />
    <result column="settle_subject_type" jdbcType="TINYINT" property="settleSubjectType" />
    <result column="settle_subject_id" jdbcType="BIGINT" property="settleSubjectId" />
    <result column="main_biz_type" jdbcType="TINYINT" property="mainBizType" />
    <result column="main_biz_id" jdbcType="BIGINT" property="mainBizId" />
    <result column="ratio" jdbcType="VARCHAR" property="ratio" />
    <result column="effect_time" jdbcType="BIGINT" property="effectTime" />
    <result column="invalid_time" jdbcType="BIGINT" property="invalidTime" />
    <result column="expired" jdbcType="TINYINT" property="expired" />
    <result column="gmt_create" jdbcType="BIGINT" property="gmtCreate" />
    <result column="gmt_update" jdbcType="BIGINT" property="gmtUpdate" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, contract_id, party_a_id, brand_contract_id, settle_subject_type, settle_subject_id, 
    main_biz_type, main_biz_id, ratio, effect_time, invalid_time, expired, gmt_create, 
    gmt_update, deleted
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contract_front_divide
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="selectUsable" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contract_front_divide cfd
    inner join contract_shop cs on cfd.contract_id = cs.contract_id
    where cs.shop_id = #{shopId,jdbcType=BIGINT}
    and cfd.deleted = 0
    and cfd.expired = 1
    and cfd.effect_time &lt;= #{time,jdbcType=BIGINT}
    and cfd.invalid_time &gt;= #{time,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from contract_front_divide
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractFrontDivideDO">
    insert into contract_front_divide (id, contract_id, party_a_id, 
      brand_contract_id, settle_subject_type, settle_subject_id, 
      main_biz_type, main_biz_id, ratio, 
      effect_time, invalid_time, expired, 
      gmt_create, gmt_update, deleted
      )
    values (#{id,jdbcType=BIGINT}, #{contractId,jdbcType=BIGINT}, #{partyAId,jdbcType=BIGINT}, 
      #{brandContractId,jdbcType=BIGINT}, #{settleSubjectType,jdbcType=TINYINT}, #{settleSubjectId,jdbcType=BIGINT}, 
      #{mainBizType,jdbcType=TINYINT}, #{mainBizId,jdbcType=BIGINT}, #{ratio,jdbcType=VARCHAR}, 
      #{effectTime,jdbcType=BIGINT}, #{invalidTime,jdbcType=BIGINT}, #{expired,jdbcType=TINYINT}, 
      #{gmtCreate,jdbcType=BIGINT}, #{gmtUpdate,jdbcType=BIGINT}, #{deleted,jdbcType=TINYINT}
      )
  </insert>
  
  <update id="updateByPrimaryKey" parameterType="so.dian.huashan.common.mapper.contract.dos.ContractFrontDivideDO">
    update contract_front_divide
    set contract_id = #{contractId,jdbcType=BIGINT},
      party_a_id = #{partyAId,jdbcType=BIGINT},
      brand_contract_id = #{brandContractId,jdbcType=BIGINT},
      settle_subject_type = #{settleSubjectType,jdbcType=TINYINT},
      settle_subject_id = #{settleSubjectId,jdbcType=BIGINT},
      main_biz_type = #{mainBizType,jdbcType=TINYINT},
      main_biz_id = #{mainBizId,jdbcType=BIGINT},
      ratio = #{ratio,jdbcType=VARCHAR},
      effect_time = #{effectTime,jdbcType=BIGINT},
      invalid_time = #{invalidTime,jdbcType=BIGINT},
      expired = #{expired,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=BIGINT},
      gmt_update = #{gmtUpdate,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
