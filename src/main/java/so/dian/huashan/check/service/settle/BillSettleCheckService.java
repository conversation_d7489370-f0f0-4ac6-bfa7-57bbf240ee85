package so.dian.huashan.check.service.settle;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import so.dian.himalaya.common.entity.PageRequest;
import so.dian.himalaya.common.exception.BizException;
import so.dian.huashan.check.emuns.settle.*;
import so.dian.huashan.check.mapper.BillSettleCheckConsistencyMapper;
import so.dian.huashan.check.mapper.BillSettleCheckDiffMapper;
import so.dian.huashan.check.mapper.entity.BillSettleCheckConsistencyDO;
import so.dian.huashan.check.mapper.entity.BillSettleCheckDiffDO;
import so.dian.huashan.check.service.settle.entity.SettlCheckProhibitionRule;
import so.dian.huashan.collection.config.BizProperties;
import so.dian.huashan.collection.mapper.BillSettleControlOrderMapper;
import so.dian.huashan.collection.mapper.BillSettleOrderOriginalMapper;
import so.dian.huashan.collection.mapper.dos.BillSettleControlOrderDO;
import so.dian.huashan.collection.mapper.dos.BillSettleOrderOriginalDO;
import so.dian.huashan.collection.mapper.example.TimeRangeExample;
import so.dian.huashan.common.enums.OrderBizType;
import so.dian.huashan.common.mapper.contract.ContractShopDivideMapper;
import so.dian.huashan.common.mapper.contract.ContractFrontDivideMapper;
import so.dian.huashan.common.mapper.contract.dos.ContractShopDivideDO;
import so.dian.huashan.common.mapper.contract.dos.ContractFrontDivideDO;
import so.dian.huashan.common.mapper.lhc.OrdersBoxMapper;
import so.dian.huashan.common.mapper.lhc.TimesCardOrderMapper;
import so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO;
import so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO;
import so.dian.huashan.common.mapper.ubud.BillingResultDetailMapper;
import so.dian.huashan.common.mapper.ubud.dos.BillingResultDetailDO;
import so.dian.huashan.common.mapper.ubud.example.BillingResultDetailExample;
import so.dian.huashan.common.utils.DateUtil;
import so.dian.huashan.framework.task.entity.ExecuteShardParam;
import so.dian.huashan.framework.task.entity.InstallShardParam;
import so.dian.huashan.framework.task.entity.ShardTaskProperties;
import so.dian.huashan.framework.transaction.ManualTransaction;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

import static so.dian.huashan.check.emuns.settle.MainBizType.BRAND_MERCHANT;
import static so.dian.huashan.check.emuns.settle.MainBizType.JOIN_MERCHANT;
import static so.dian.huashan.check.emuns.settle.MainBizType.NORMAL_MERCHANT;
import static so.dian.huashan.check.job.settle.SettleCheckJob.SETTLE_CHECK_JOB_NAME;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_CHECK_STATUS_ILLEGAL;
import static so.dian.huashan.common.enums.BizErrorCodeEnum.SETTLE_ORDER_STATUS_IS_NULL;
import static so.dian.huashan.common.exception.LogMarkerFactory.BILLING_SETTLE;

/**
 * @author: miaoshuai
 * @create: 2023/12/15 10:26
 * @description:
 */
@Slf4j
@Service
public class BillSettleCheckService extends BillSettleBaseCheck {

    private final static String MAX_ID = "max.id";

    @Autowired
    private BillSettleOrderOriginalMapper settleOrderOriginalMapper;

    @Autowired
    private BillSettleControlOrderMapper settleControlOrderMapper;

    @Autowired
    private ContractShopDivideMapper contractShopDivideMapper;

    @Autowired
    private ContractFrontDivideMapper contractFrontDivideMapper;

    @Autowired
    private TimesCardOrderMapper timesCardOrderMapper;

    @Autowired
    private OrdersBoxMapper ordersBoxMapper;

    @Autowired
    private BillingResultDetailMapper billingResultDetailMapper;

    @Autowired
    private BillSettleCheckDiffMapper settleCheckDiffMapper;

    @Autowired
    private BillSettleCheckConsistencyMapper settleCheckConsistencyMapper;

    @Autowired
    private BillSettleCheckService self;

    @Autowired
    private BizProperties bizProperties;

    public List<String> listIds(InstallShardParam shardParam) {

        TimeRangeExample timeRange = calcTimeRange(shardParam);
        Long maxId = shardParam.getExtParam(MAX_ID);
        List<Long> ids = settleOrderOriginalMapper.selectIdByTimeRange(maxId, timeRange, shardParam.getPageSize());
        shardParam.putExtParam(MAX_ID, CollUtil.getLast(ids));

        return ids.stream().map(Object::toString).collect(Collectors.toList());
    }

    /**
     * 增量对账入口
     * @param shardParam 任务分片执行参数
     */
    public void incrementCheck(ExecuteShardParam shardParam) {

        List<Long> ids = shardParam.bizIdToLong();
        if (CollUtil.isEmpty(ids))
            return;

        List<BillSettleOrderOriginalDO> orderOriginalDOS = settleOrderOriginalMapper.selectByIds(ids);
        if (CollUtil.isEmpty(orderOriginalDOS))
            return;

        // 根据订单编码查询抽单池
        List<String> orderNos = orderOriginalDOS.stream().map(BillSettleOrderOriginalDO::getOrderNo).collect(Collectors.toList());
        List<BillSettleControlOrderDO> controlOrderDOS = settleControlOrderMapper.selectByOrderNos(orderNos);
        Map<String, List<BillSettleControlOrderDO>> controlOrderMap = controlOrderDOS.stream().collect(Collectors.groupingBy(this::getControlOrderKey));

        List<BillSettleOrderOriginalDO> toModifyOriginalDOS = Lists.newArrayList();
        List<BillSettleCheckDiffDO> checkDiffDOS = Lists.newArrayList();
        List<BillSettleCheckConsistencyDO> consistencyDOS = Lists.newArrayList();

        for (BillSettleOrderOriginalDO orderOriginalDO : orderOriginalDOS) {

            try {
                SettleOriginalStatus originalStatus = doCheck(orderOriginalDO, controlOrderMap);

                switch (originalStatus) {
                    case DIFF:
                        BillSettleCheckDiffDO checkDiffDO = new BillSettleCheckDiffDO();
                        checkDiffDO.setOriginalId(orderOriginalDO.getId());
                        checkDiffDO.setOrderNo(orderOriginalDO.getOrderNo());
                        checkDiffDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        checkDiffDO.setVersion(0);
                        checkDiffDO.init();
                        checkDiffDOS.add(checkDiffDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.DIFF.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    case NO_CHECK:
                        orderOriginalDO.setStatus(SettleOriginalStatus.NO_CHECK.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    case CONSISTENCY:
                        BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
                        consistencyDO.setOriginalId(orderOriginalDO.getId());
                        consistencyDO.setOrderNo(orderOriginalDO.getOrderNo());
                        consistencyDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        consistencyDO.setCheckType(ConsistencyCheckType.INCREMENT.getCode());
                        consistencyDO.init();
                        consistencyDOS.add(consistencyDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.CONSISTENCY.getCode());
                        toModifyOriginalDOS.add(orderOriginalDO);
                        break;
                    default:
                        throw BizException.create(SETTLE_CHECK_STATUS_ILLEGAL);
                }
            }catch (Exception e) {
                log.error(BILLING_SETTLE, "清结算业务对账失败，订单[{}], 分片子任务[{}]", orderOriginalDO.getOrderNo(), shardParam.getSubTaskId());
                throw e;
            }
        }

        self.batchSave(toModifyOriginalDOS, checkDiffDOS, consistencyDOS);
    }

    /**
     * 差异对账入口
     */
    public void diffCheck() {

        log.info(BILLING_SETTLE, "开始执行差异对账");
        long pageNo = 1;
        List<BillSettleCheckDiffDO> checkDiffDOS = settleCheckDiffMapper.selectReCheck(PageRequest.of(pageNo, 500));

        int loopCount = 0;
        while (CollUtil.isNotEmpty(checkDiffDOS)) {
            if (++ loopCount == bizProperties.getSettle().getMaxLoop()) {
                log.warn(BILLING_SETTLE, "清结算差异对账while循环已经达到最高次数");
            }

            List<Long> orifinalIds = checkDiffDOS.stream().map(BillSettleCheckDiffDO::getOriginalId)
                    .collect(Collectors.toList());
            List<BillSettleOrderOriginalDO> orderOriginalDOS = settleOrderOriginalMapper.selectByIds(orifinalIds);
            if (CollUtil.isEmpty(orderOriginalDOS))
                return;

            Map<Long, BillSettleOrderOriginalDO> idAndOriginalMap = orderOriginalDOS.stream()
                    .collect(Collectors.toMap(BillSettleOrderOriginalDO::getId, Function.identity()));

            // 根据订单编码查询抽单池
            List<String> orderNos = orderOriginalDOS.stream().map(BillSettleOrderOriginalDO::getOrderNo).collect(Collectors.toList());
            List<BillSettleControlOrderDO> controlOrderDOS = settleControlOrderMapper.selectByOrderNos(orderNos);
            Map<String, List<BillSettleControlOrderDO>> controlOrderMap = controlOrderDOS.stream().collect(Collectors.groupingBy(this::getControlOrderKey));

            List<Long> toDeleteDiffs = Lists.newArrayList();
            List<Long> toAddVersionDiffs = Lists.newArrayList();
            List<BillSettleOrderOriginalDO> toUpdateOriginals = Lists.newArrayList();
            List<BillSettleCheckConsistencyDO> toSaveConsistencys = Lists.newArrayList();
            for (BillSettleCheckDiffDO checkDiffDO : checkDiffDOS) {
                BillSettleOrderOriginalDO orderOriginalDO = idAndOriginalMap.get(checkDiffDO.getOriginalId());
                if (Objects.isNull(orderOriginalDO)) {
                    log.warn(BILLING_SETTLE, "差异对账查询不到订单底表数据, 差异数据ID:[{}]", checkDiffDO.getId());
                    continue;
                }

                SettleOriginalStatus originalStatus = doCheck(orderOriginalDO, controlOrderMap);
                switch (originalStatus) {
                    case DIFF:
                        toAddVersionDiffs.add(checkDiffDO.getId());
                        break;
                    case NO_CHECK:
                        orderOriginalDO.setStatus(SettleOriginalStatus.NO_CHECK.getCode());
                        toUpdateOriginals.add(orderOriginalDO);
                        toDeleteDiffs.add(checkDiffDO.getId());
                        break;
                    case CONSISTENCY:
                        BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
                        consistencyDO.setOriginalId(orderOriginalDO.getId());
                        consistencyDO.setOrderNo(orderOriginalDO.getOrderNo());
                        consistencyDO.setOrderStatus(orderOriginalDO.getOrderStatus());
                        consistencyDO.setCheckType(ConsistencyCheckType.DIFF.getCode());
                        toSaveConsistencys.add(consistencyDO);

                        orderOriginalDO.setStatus(SettleOriginalStatus.DIFF_CONSISTENCY.getCode());
                        toUpdateOriginals.add(orderOriginalDO);

                        toDeleteDiffs.add(checkDiffDO.getId());
                        break;
                    default:
                        throw BizException.create(SETTLE_CHECK_STATUS_ILLEGAL);
                }
            }

            self.batchSaveDiff(toDeleteDiffs, toUpdateOriginals, toSaveConsistencys, toAddVersionDiffs);

            log.info(BILLING_SETTLE, "差异对账执行完成一批");
            checkDiffDOS = settleCheckDiffMapper.selectReCheck(PageRequest.of(++ pageNo, 500));
        }
    }

    @Transactional
    public void batchSaveDiff(List<Long> toDeleteDiffs, List<BillSettleOrderOriginalDO> toUpdateOriginals, List<BillSettleCheckConsistencyDO> toSaveConsistencys, List<Long> toAddVersionDiffs) {
        if (CollUtil.isNotEmpty(toDeleteDiffs))
            settleCheckDiffMapper.batchDeleteByIds(toDeleteDiffs);

        if (CollUtil.isNotEmpty(toAddVersionDiffs))
            settleCheckDiffMapper.batchAddVersion(toAddVersionDiffs);

        if (CollUtil.isNotEmpty(toUpdateOriginals))
            settleOrderOriginalMapper.batchUpdate(toUpdateOriginals);

        if (CollUtil.isNotEmpty(toSaveConsistencys))
            settleCheckConsistencyMapper.batchInsert(toSaveConsistencys);
    }

    @Transactional
    public void batchSave(List<BillSettleOrderOriginalDO> orderOriginalDOS, List<BillSettleCheckDiffDO> checkDiffDOS, List<BillSettleCheckConsistencyDO> consistencyDOS) {
        if (CollUtil.isNotEmpty(orderOriginalDOS))
            settleOrderOriginalMapper.batchUpdate(orderOriginalDOS);

        if (CollUtil.isNotEmpty(checkDiffDOS))
            settleCheckDiffMapper.batchInsert(checkDiffDOS);

        if (CollUtil.isNotEmpty(consistencyDOS))
            settleCheckConsistencyMapper.batchInsert(consistencyDOS);
    }

    /**
     * 手动平账
     * @param diffIds 清结算差异表ID集合
     */
    public void manualConsistency(List<Long> diffIds) {
        if (CollUtil.isEmpty(diffIds))
            return;

        List<BillSettleCheckDiffDO> checkDiffDOS = settleCheckDiffMapper.selectByIds(diffIds);
        if (CollUtil.isEmpty(checkDiffDOS))
            return;

        List<BillSettleOrderOriginalDO> toUpdateOriginals = Lists.newArrayList();
        List<BillSettleCheckConsistencyDO> toSaveConsistencys = Lists.newArrayList();
        for (BillSettleCheckDiffDO checkDiffDO : checkDiffDOS) {
            BillSettleCheckConsistencyDO consistencyDO = new BillSettleCheckConsistencyDO();
            consistencyDO.setOriginalId(checkDiffDO.getOriginalId());
            consistencyDO.setOrderNo(checkDiffDO.getOrderNo());
            consistencyDO.setOrderStatus(checkDiffDO.getOrderStatus());
            consistencyDO.setCheckType(ConsistencyCheckType.DIFF.getCode());
            toSaveConsistencys.add(consistencyDO);

            BillSettleOrderOriginalDO orderOriginalDO = new BillSettleOrderOriginalDO();
            orderOriginalDO.setId(checkDiffDO.getOriginalId());
            orderOriginalDO.setStatus(SettleOriginalStatus.DIFF_CONSISTENCY.getCode());
            toUpdateOriginals.add(orderOriginalDO);
        }

        ManualTransaction transaction = ManualTransaction.builder().build();
        transaction.invoke(r -> {
            if (CollUtil.isNotEmpty(diffIds))
                settleCheckDiffMapper.batchDeleteByIds(diffIds);

            if (CollUtil.isNotEmpty(toUpdateOriginals))
                settleOrderOriginalMapper.batchUpdate(toUpdateOriginals);

            if (CollUtil.isNotEmpty(toSaveConsistencys))
                settleCheckConsistencyMapper.batchInsert(toSaveConsistencys);
        });
    }

    private SettleOriginalStatus doCheck(BillSettleOrderOriginalDO orderOriginalDO, Map<String, List<BillSettleControlOrderDO>> controlOrderMap) {
        // 根据业务类型进行不同的对账逻辑
        if (orderOriginalDO.getBizType().equals(OrderBizType.ORDER_BOX_ORDER.getKey())) {
            return checkRentalOrder(orderOriginalDO, controlOrderMap);
        } else if (orderOriginalDO.getBizType().equals(OrderBizType.TIMES_CARD_ORDER.getKey())) {
            return checkTimesCardOrder(orderOriginalDO, controlOrderMap);
        } else if (orderOriginalDO.getBizType().equals(OrderBizType.TIMES_CARD_VERIFY.getKey())) {
            return checkTimesCardVerify(orderOriginalDO, controlOrderMap);
        } else {
            // 其他情况，对账状态=一致
            return SettleOriginalStatus.CONSISTENCY;
        }
    }

    /**
     * 租借订单对账逻辑
     */
    private SettleOriginalStatus checkRentalOrder(BillSettleOrderOriginalDO orderOriginalDO, Map<String, List<BillSettleControlOrderDO>> controlOrderMap) {
        // 根据[借出门店ID]和[租赁订单支付成功时间]查询分成主数据列表(contract_divide.contract_shop_divide)
        List<ContractShopDivideDO> shopDivideDOS = contractShopDivideMapper.selectUsable(orderOriginalDO.getLoanShopId(), orderOriginalDO.getPaySuccessTime().getTime());
        if (CollUtil.isEmpty(shopDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        // 过滤掉不满足[分成比例判断]、[抽单池判断]、[禁入规则判断]等条件的分成主数据
        SettlCheckProhibitionRule prohibitionRule = new SettlCheckProhibitionRule(orderOriginalDO);
        List<MainBizType> checkRatioTypes = Lists.newArrayList(NORMAL_MERCHANT, BRAND_MERCHANT, JOIN_MERCHANT);
        shopDivideDOS = shopDivideDOS.stream()
                .filter(shopDivideDO -> {
                    MainBizType mainBizType = MainBizType.from(shopDivideDO.getMainBizType());
                    if (checkRatioTypes.stream().anyMatch(type -> type.equals(mainBizType))) {
                        if (Objects.nonNull(shopDivideDO.getRatio()) && shopDivideDO.getRatio() > 0) {
                            return Boolean.TRUE;
                        } else {
                            return Boolean.FALSE;
                        }
                    }
                    return Boolean.TRUE;
                })
                .filter(shopDivideDO -> { // 抽单池判断
                    BillSettleControlOrderDO tmpControl = new BillSettleControlOrderDO();
                    tmpControl.setOrderNo(orderOriginalDO.getOrderNo());
                    tmpControl.setMainBizId(shopDivideDO.getMainBizId());
                    tmpControl.setMainBizType(shopDivideDO.getMainBizType().intValue());
                    tmpControl.setSettleSubjectType(shopDivideDO.getSettleSubjectType().intValue());
                    tmpControl.setSettleSubjectId(shopDivideDO.getSettleSubjectId());
                    List<BillSettleControlOrderDO> settleControlOrderDOS = controlOrderMap.get(getControlOrderKey(tmpControl));
                    return CollUtil.isEmpty(settleControlOrderDOS);
                })
                .filter(shopDivideDO -> !prohibitionRule.isHit(shopDivideDO)) // 禁入规则判断
                .collect(Collectors.toList());

        // 过滤筛选结束后，若分成主数据列表为空，则对账状态=不对账
        if (CollUtil.isEmpty(shopDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        // 根据以下条件查询计费明细列表，若计费明细列表为空，则对账状态=差异
        for (ContractShopDivideDO shopDivideDO : shopDivideDOS) {
            BillingResultDetailExample detailExample = BillingResultDetailExample.builder()
                    .bizNo(orderOriginalDO.getOrderNo())
                    .settleTargetId(shopDivideDO.getMainBizId().toString())
                    .settleTargetType(shopDivideDO.getMainBizType().toString())
                    .tradeType(SettleOrderStatus.PAY.getCode().equals(orderOriginalDO.getOrderStatus()) ? TradeType.PAYMENT.getCode() : TradeType.REFUND.getCode())
                    .build();
            List<BillingResultDetailDO> resultDetailDOS = billingResultDetailMapper.selectByExample(detailExample);
            if (CollUtil.isEmpty(resultDetailDOS)) {
                return SettleOriginalStatus.DIFF;
            }
        }

        return SettleOriginalStatus.CONSISTENCY;
    }

    /**
     * 次卡订单对账逻辑
     */
    private SettleOriginalStatus checkTimesCardOrder(BillSettleOrderOriginalDO orderOriginalDO, Map<String, List<BillSettleControlOrderDO>> controlOrderMap) {
        // 查询次卡订单信息
        TimesCardOrderDO timesCardOrderDO = timesCardOrderMapper.selectByOrderNo(orderOriginalDO.getOrderNo());
        if (timesCardOrderDO == null) {
            log.error("次卡订单不存在: orderNo={}", orderOriginalDO.getOrderNo());
            return SettleOriginalStatus.DIFF;
        }

        // 根据[借出门店ID]和[次卡订单创建时间]查询分成主数据列表(contract_divide.contract_front_divide)
        List<ContractFrontDivideDO> frontDivideDOS = contractFrontDivideMapper.selectUsable(orderOriginalDO.getLoanShopId(), timesCardOrderDO.getCreateTime().getTime());
        if (CollUtil.isEmpty(frontDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        // 判断次卡订单是否是【前端分账订单】
        boolean isFrontSplitOrder = isFrontDivideOrder(timesCardOrderDO);
        if (isFrontSplitOrder) {
            // 若是前端分账订单，则过滤掉不满足【结算主体=代理商/合资公司，且结算对象=代理商/合资公司】这种情况的分成主数据
            frontDivideDOS = frontDivideDOS.stream()
                    .filter(divideDO -> (divideDO.getSettleSubjectType().equals(SettleSubjectType.AGENT.getCode()) ||
                                       divideDO.getSettleSubjectType().equals(SettleSubjectType.JV_COMPANY.getCode())) &&
                                      (divideDO.getMainBizType().equals(MainBizType.AGENT.getCode()) ||
                                       divideDO.getMainBizType().equals(MainBizType.JV_COMPANY.getCode())))
                    .collect(Collectors.toList());
        } else {
            // 若不是前端分账支付订单，则过滤掉不满足【结算主体=小电，且结算对象=代理商/合资公司】这种情况的分成主数据
            frontDivideDOS = frontDivideDOS.stream()
                    .filter(divideDO -> divideDO.getSettleSubjectType().equals(SettleSubjectType.XIAODIAN.getCode()) &&
                                      (divideDO.getMainBizType().equals(MainBizType.AGENT.getCode()) ||
                                       divideDO.getMainBizType().equals(MainBizType.JV_COMPANY.getCode())))
                    .collect(Collectors.toList());
        }

        // 过滤筛选结束后，若分成主数据列表为空，则对账状态=不对账
        if (CollUtil.isEmpty(frontDivideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        // 根据bizNo(次卡订单号)查询计费明细列表，若计费明细列表为空，则对账状态=差异
        List<BillingResultDetailDO> resultDetailDOS = getAllBillingDetailsByBizNo(orderOriginalDO.getOrderNo());
        if (CollUtil.isEmpty(resultDetailDOS)) {
            return SettleOriginalStatus.DIFF;
        }

        // 判断次卡订单状态
        SettleOrderStatus orderStatus = SettleOrderStatus.from(orderOriginalDO.getOrderStatus());
        if (Objects.isNull(orderStatus)) {
            throw BizException.create(SETTLE_ORDER_STATUS_IS_NULL);
        }

        if (orderStatus.equals(SettleOrderStatus.CARD_PAY)) {
            // 若状态是次卡支付，且不存在符合条件的计费明细(mainBizType=99，amount_input = pay_amount * 0.994 四舍五入，trade_type=1)，则对账状态=差异
            long expectedAmount = Math.round(orderOriginalDO.getPayAmount() * 0.994);
            boolean foundMatchingDetail = resultDetailDOS.stream()
                    .anyMatch(detail -> detail.getSettleTargetType().equals("99") &&
                                      detail.getAmountInput().equals(expectedAmount) &&
                                      detail.getTradeType().equals(TradeType.PAYMENT.getCode()));
            if (!foundMatchingDetail) {
                return SettleOriginalStatus.DIFF;
            }
        } else if (orderStatus.equals(SettleOrderStatus.CARD_REFUND)) {
            // 若状态是次卡退款，按settleTargetId分组，每个分组若没有同时存在tradeType=1和tradeType=2的计费明细，则对账状态=差异
            Map<String, List<BillingResultDetailDO>> groupedByTargetId = resultDetailDOS.stream()
                    .collect(Collectors.groupingBy(BillingResultDetailDO::getSettleTargetId));

            for (List<BillingResultDetailDO> group : groupedByTargetId.values()) {
                boolean hasPayment = group.stream().anyMatch(detail -> detail.getTradeType().equals(TradeType.PAYMENT.getCode()));
                boolean hasRefund = group.stream().anyMatch(detail -> detail.getTradeType().equals(TradeType.REFUND.getCode()));
                if (!(hasPayment && hasRefund)) {
                    return SettleOriginalStatus.DIFF;
                }
            }
        }

        return SettleOriginalStatus.CONSISTENCY;
    }

    /**
     * 次卡核销对账逻辑
     */
    private SettleOriginalStatus checkTimesCardVerify(BillSettleOrderOriginalDO orderOriginalDO, Map<String, List<BillSettleControlOrderDO>> controlOrderMap) {
        // 根据[借出门店ID]和[租赁订单创建时间]查询分成主数据列表
        List<ContractShopDivideDO> divideDOS;
        // 查询租赁订单信息
        OrdersBoxDO ordersBoxDO = ordersBoxMapper.selectByOrderNo(orderOriginalDO.getOrderNo());
        if (ordersBoxDO == null) {
            log.warn("租赁订单不存在: orderNo={}", orderOriginalDO.getOrderNo());
            return SettleOriginalStatus.DIFF;
        }
        // 判断租赁订单核销的次卡对应订单是否为【前端分账订单】
        boolean isFrontSplitOrder = isFrontDivideOrder(ordersBoxDO);

        if (isFrontSplitOrder) {
            // 若是前端分账订单，分成主数据查询contract_divide.contract_front_divide这张表
            List<ContractFrontDivideDO> frontDivideDOS = contractFrontDivideMapper.selectUsable(orderOriginalDO.getLoanShopId(), ordersBoxDO.getCreateTime().getTime());
            // 将ContractFrontDivideDO转换为ContractShopDivideDO以保持接口一致性
            divideDOS = convertFrontDivideToShopDivide(frontDivideDOS);
        } else {
            // 若不是前端分账订单，分成主数据查询contract_divide.contract_shop_divide这张表
            divideDOS = contractShopDivideMapper.selectUsable(orderOriginalDO.getLoanShopId(), ordersBoxDO.getCreateTime().getTime());
        }

        if (CollUtil.isEmpty(divideDOS)) {
            return SettleOriginalStatus.NO_CHECK;
        }

        // 过滤掉不满足【分成主数据.mainBizType in (4,11)】这种情况的分成主数据
        divideDOS = divideDOS.stream()
                .filter(divideDO -> divideDO.getMainBizType().equals(MainBizType.AGENT.getCode()) ||
                                  divideDO.getMainBizType().equals(MainBizType.JV_COMPANY.getCode()))
                .collect(Collectors.toList());

        // 过滤筛选结束后，若分成主数据列表为空，则对账状态=差异
        if (CollUtil.isEmpty(divideDOS)) {
            return SettleOriginalStatus.DIFF;
        }

        // 根据bizNo(次卡订单号)查询计费明细列表，若计费明细列表为空，则对账状态=差异
        // 次卡核销时，需要查询对应的次卡订单号
        String timesCardOrderNo = getTimesCardOrderNoFromVerifyOrder(orderOriginalDO.getOrderNo());
        List<BillingResultDetailDO> resultDetailDOS = getAllBillingDetailsByBizNo(timesCardOrderNo);
        if (CollUtil.isEmpty(resultDetailDOS)) {
            return SettleOriginalStatus.DIFF;
        }

        // 若不存在符合条件的计费明细(settleTargetId=分成主数据.mainBizId，settleTargetType in (4,11)，trade_type=5)，则对账状态=差异
        boolean foundMatchingDetail = false;
        for (ContractShopDivideDO divideDO : divideDOS) {
            boolean hasMatchingDetail = resultDetailDOS.stream()
                    .anyMatch(detail -> detail.getSettleTargetId().equals(divideDO.getMainBizId().toString()) &&
                                      (detail.getSettleTargetType().equals("4") || detail.getSettleTargetType().equals("11")) &&
                                      detail.getTradeType().equals(TradeType.TIME_CARD_VERIFY.getCode())); // trade_type=5 (次卡订单核销)
            if (hasMatchingDetail) {
                foundMatchingDetail = true;
                break;
            }
        }
        if (!foundMatchingDetail) {
            return SettleOriginalStatus.DIFF;
        }

        // 若存在trade_type = 1的计费明细
        boolean hasPaymentType = resultDetailDOS.stream()
                .anyMatch(detail -> detail.getTradeType().equals(TradeType.PAYMENT.getCode()));

        if (hasPaymentType) {
            // 若【计费明细数量A】不等于【计费明细数量B】，则对账状态=差异
            // 计费明细数量A(settleTargetType = 99 & trade_type = 5)
            long countA = resultDetailDOS.stream()
                    .filter(detail -> detail.getSettleTargetType().equals("99") &&
                                    detail.getTradeType().equals(TradeType.TIME_CARD_VERIFY.getCode()))
                    .count();

            // 计费明细数量B(settleTargetType in (4,11) & trade_type = 5 & bizCode.startsWith("FD_"))
            long countB = resultDetailDOS.stream()
                    .filter(detail -> (detail.getSettleTargetType().equals("4") || detail.getSettleTargetType().equals("11")) &&
                                    detail.getTradeType().equals(TradeType.TIME_CARD_VERIFY.getCode()) &&
                                    detail.getBizCode() != null && detail.getBizCode().startsWith("FD_"))
                    .count();

            if (countA != countB) {
                return SettleOriginalStatus.DIFF;
            }
        }

        return SettleOriginalStatus.CONSISTENCY;
    }

    /**
     * 判断次卡订单是否为【前端分账订单】
     * @param timesCardOrderDO 次卡订单
     * @return 是否为【前端分账订单】
     */
    private boolean isFrontDivideOrder(TimesCardOrderDO timesCardOrderDO) {
        if (timesCardOrderDO == null || timesCardOrderDO.getProfitSharding() == null) {
            return false;
        }
        // profitSharding = 2 表示前端分账
        return timesCardOrderDO.getProfitSharding() == 2;
    }

    /**
     * 判断租赁订单核销的次卡对应订单是否为【前端分账订单】
     * @param ordersBoxDO 租赁订单
     * @return 是否为【前端分账订单】
     */
    private boolean isFrontDivideOrder(OrdersBoxDO ordersBoxDO) {
        JSONObject loanPriceInfo = JSON.parseObject(ordersBoxDO.getLoanPriceInfo());
        if (loanPriceInfo == null) {
            return false;
        }

        Integer profitSharding = loanPriceInfo.getInteger("profitSharding");
        if (profitSharding == null) {
            return false;
        }

        // profitSharding = 2 表示前端分账
        return profitSharding == 2;
    }

    /**
     * 将ContractFrontDivideDO转换为ContractShopDivideDO以保持接口一致性
     * @param frontDivideDOS 前端分成主数据列表
     * @return 门店分成主数据列表
     */
    private List<ContractShopDivideDO> convertFrontDivideToShopDivide(List<ContractFrontDivideDO> frontDivideDOS) {
        if (CollUtil.isEmpty(frontDivideDOS)) {
            return Lists.newArrayList();
        }

        return frontDivideDOS.stream().map(frontDivide -> {
            ContractShopDivideDO shopDivide = new ContractShopDivideDO();
            shopDivide.setId(frontDivide.getId());
            shopDivide.setContractId(frontDivide.getContractId());
            shopDivide.setSettleSubjectType(frontDivide.getSettleSubjectType());
            shopDivide.setSettleSubjectId(frontDivide.getSettleSubjectId());
            shopDivide.setMainBizType(frontDivide.getMainBizType());
            shopDivide.setMainBizId(frontDivide.getMainBizId());
            shopDivide.setEffectTime(frontDivide.getEffectTime());
            shopDivide.setInvalidTime(frontDivide.getInvalidTime());
            shopDivide.setBrandContractId(frontDivide.getBrandContractId());
            shopDivide.setExpired(frontDivide.getExpired());
            shopDivide.setGmtCreate(frontDivide.getGmtCreate());
            shopDivide.setGmtUpdate(frontDivide.getGmtUpdate());
            shopDivide.setDeleted(frontDivide.getDeleted());
            // 注意：前端分成的ratio是String类型，门店分成的ratio是Integer类型
            try {
                shopDivide.setRatio(Integer.valueOf(frontDivide.getRatio()));
            } catch (NumberFormatException e) {
                log.warn("前端分成比例转换失败: ratio={}", frontDivide.getRatio());
                shopDivide.setRatio(0);
            }
            return shopDivide;
        }).collect(Collectors.toList());
    }

    /**
     * 从次卡核销订单号获取对应的次卡订单号
     * @param verifyOrderNo 次卡核销订单号
     * @return 次卡订单号
     */
    private String getTimesCardOrderNoFromVerifyOrder(String verifyOrderNo) {
        // 这里需要根据实际业务逻辑来实现
        // 可能需要查询相关的映射表或者根据订单号规则来转换
        // 暂时返回原订单号，实际项目中需要根据具体业务逻辑实现
        // TODO 如何通过租赁订单号查询到次卡订单号？
        return verifyOrderNo;
    }

    /**
     * 根据bizNo查询所有计费明细
     */
    private List<BillingResultDetailDO> getAllBillingDetailsByBizNo(String bizNo) {
        try {
            return billingResultDetailMapper.selectByBizNo(bizNo);
        } catch (Exception e) {
            log.error("查询计费明细失败: bizNo={}", bizNo, e);
            return Lists.newArrayList();
        }
    }

    private String getControlOrderKey(BillSettleControlOrderDO controlOrderDO) {
        return StrUtil.join(StrUtil.DOT,
                controlOrderDO.getOrderNo(),
                controlOrderDO.getMainBizType(),
                controlOrderDO.getMainBizId(),
                controlOrderDO.getSettleSubjectType(),
                controlOrderDO.getSettleSubjectId());
    }

    public void dataDelete() {
        DateTime dateTime = DateUtil.offsetDay(new Date(), -3);
        dateTime = DateUtil.beginOfDay(dateTime);
        Long originalmaxId = settleOrderOriginalMapper.selectMaxIdByGmtCreate(dateTime.getTime());
        Long consistencyMaxId = settleCheckConsistencyMapper.selectMaxIdByGmtCreate(dateTime.getTime());

        int loopCount = 0;
        Integer deleteSize = Optional.ofNullable(bizProperties.getSettle().getDeleteSize()).orElse(100000);
        while (Objects.nonNull(originalmaxId) || Objects.nonNull(consistencyMaxId)) {
            if (++ loopCount == bizProperties.getSettle().getMaxLoop()) {
                log.warn(BILLING_SETTLE, "清结算数据归档while循环已经达到最高次数");
                return;
            }

            if (Objects.nonNull(originalmaxId)) {
                int result = settleOrderOriginalMapper.deleteByGmtCreateAndMaxId(dateTime.getTime(), originalmaxId, deleteSize);
                log.info(BILLING_SETTLE, "原始底表删除完成一批, gmtCreate={}, maxId={}, result={}", dateTime.getTime(), originalmaxId, result);
                originalmaxId = settleOrderOriginalMapper.selectMaxIdByGmtCreate(dateTime.getTime());
            }


            if (Objects.nonNull(consistencyMaxId)) {
                int result = settleCheckConsistencyMapper.deleteByGmtCreateAndMaxId(dateTime.getTime(), consistencyMaxId, deleteSize);
                log.info(BILLING_SETTLE, "对账一致表删除完成一批, gmtCreate={}, maxId={}, result={}", dateTime.getTime(), consistencyMaxId, result);
                consistencyMaxId = settleCheckConsistencyMapper.selectMaxIdByGmtCreate(dateTime.getTime());
            }

            ThreadUtil.sleep(2, TimeUnit.SECONDS);
        }
    }

    @Override
    protected ShardTaskProperties getProperties() {
        return new ShardTaskProperties(SETTLE_CHECK_JOB_NAME);
    }

    @Override
    protected Function<InstallShardParam, List<String>> findIdsFunc() {
        return this::listIds;
    }

    @Override
    protected Consumer<ExecuteShardParam> bizProcessService() {
        return this::incrementCheck;
    }
}
