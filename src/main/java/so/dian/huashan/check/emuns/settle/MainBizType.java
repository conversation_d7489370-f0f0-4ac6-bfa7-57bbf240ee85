package so.dian.huashan.check.emuns.settle;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/12/15 16:46
 * @description: 结算对象类型
 */
@AllArgsConstructor
@Getter
public enum MainBizType {
    NORMAL_MERCHANT((byte) 1, "普通商户"),
    BRAND_MERCHANT((byte) 2, "品牌商户"),
    JOIN_MERCHANT((byte) 3, "加盟商户"),
    AGENT((byte) 4, "代理商/二级代理商"),
    JV_COMPANY((byte) 11, "合资公司"),
    XIAODIAN((byte) 99, "小电"),
    ;

    private final Byte code;
    private final String desc;

    public static MainBizType from(Byte code) {
        return Arrays.stream(values()).filter(type -> type.code.equals(code)).findFirst().orElse(null);
    }
}
