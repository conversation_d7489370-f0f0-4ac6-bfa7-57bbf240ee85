package so.dian.huashan.common.mapper.thor.entity;

import lombok.Data;

import java.util.Date;

/**
 * 次卡优惠券和次卡订单关系表
 * <AUTHOR>
 */
@Data
public class TimesCardCouponOrderMappingDO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 优惠券case_id，唯一索引
     */
    private Long couponCaseId;
    
    /**
     * 次卡订单编号
     */
    private String timesCardOrderNo;
    
    /**
     * 是否删除：0-未删除；1-已删除
     */
    private Integer deleted;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建时间戳
     */
    private Long gmtCreate;
    
    /**
     * 更新时间戳
     */
    private Long gmtUpdate;
}
