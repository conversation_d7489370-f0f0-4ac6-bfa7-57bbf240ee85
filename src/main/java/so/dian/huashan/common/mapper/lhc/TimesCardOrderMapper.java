package so.dian.huashan.common.mapper.lhc;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.lhc.entity.TimesCardOrderDO;

/**
 * TimesCardOrderMapper
 * <AUTHOR>
public interface TimesCardOrderMapper {
    
    /**
     * 根据订单号查询次卡订单
     * @param orderNo 订单号
     * @return 次卡订单信息
     */
    TimesCardOrderDO selectByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 次卡订单信息
     */
    TimesCardOrderDO selectByPrimaryKey(@Param("id") Long id);
    
    /**
     * 插入记录
     * @param record 次卡订单信息
     * @return 影响行数
     */
    int insert(TimesCardOrderDO record);
    
    /**
     * 更新记录
     * @param record 次卡订单信息
     * @return 影响行数
     */
    int updateByPrimaryKey(TimesCardOrderDO record);
    
    /**
     * 删除记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("id") Long id);
}
