package so.dian.huashan.common.mapper.contract.dos;

import java.io.Serializable;
import lombok.Data;

/**
 * contract_front_divide
 * <AUTHOR>
@Data
public class ContractFrontDivideDO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 甲方id
     */
    private Long partyAId;

    /**
     * 品牌合同id
     */
    private Long brandContractId;

    /**
     * 结算方类型: 1 小电, 2 代理商, 5 合资公司
     */
    private Byte settleSubjectType;

    /**
     * 结算方id
     */
    private Long settleSubjectId;

    /**
     * 主体类型: 1 普通商户, 2 品牌商户, 3 加盟商户, 4 代理商, 11 合资公司, 99 小电
     */
    private Byte mainBizType;

    /**
     * 主体id
     */
    private Long mainBizId;

    /**
     * 分成比例
     */
    private String ratio;

    /**
     * 生效时间
     */
    private Long effectTime;

    /**
     * 失效时间
     */
    private Long invalidTime;

    /**
     * 是否过期: 1 没过期, 2 过期
     */
    private Byte expired;

    /**
     * 创建时间
     */
    private Long gmtCreate;

    /**
     * 更新时间
     */
    private Long gmtUpdate;

    /**
     * 逻辑删除：0 未删除，1 已删除
     */
    private Byte deleted;

    private static final long serialVersionUID = 1L;
}
