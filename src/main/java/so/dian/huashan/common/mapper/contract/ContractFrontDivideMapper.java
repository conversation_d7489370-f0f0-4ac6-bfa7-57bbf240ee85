package so.dian.huashan.common.mapper.contract;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.contract.dos.ContractFrontDivideDO;

import java.util.List;

/**
 * ContractFrontDivideMapper
 * <AUTHOR>
public interface ContractFrontDivideMapper {
    
    /**
     * 根据门店ID和时间查询有效的前端分成主数据
     * @param shopId 门店ID
     * @param time 时间戳
     * @return 分成主数据列表
     */
    List<ContractFrontDivideDO> selectUsable(@Param("shopId") Long shopId, @Param("time") Long time);
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 分成主数据
     */
    ContractFrontDivideDO selectByPrimaryKey(@Param("id") Long id);
    
    /**
     * 插入记录
     * @param record 分成主数据
     * @return 影响行数
     */
    int insert(ContractFrontDivideDO record);
    
    /**
     * 更新记录
     * @param record 分成主数据
     * @return 影响行数
     */
    int updateByPrimaryKey(ContractFrontDivideDO record);
    
    /**
     * 删除记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("id") Long id);
}
