package so.dian.huashan.common.mapper.thor;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.thor.entity.TimesCardCouponOrderMappingDO;

/**
 * 次卡优惠券和次卡订单关系表Mapper
 * <AUTHOR>
 */
public interface TimesCardCouponOrderMappingMapper {
    
    /**
     * 根据优惠券case_id查询次卡优惠券订单映射
     * @param couponCaseId 优惠券case_id
     * @return 次卡优惠券订单映射信息
     */
    TimesCardCouponOrderMappingDO selectByCouponCaseId(@Param("couponCaseId") Long couponCaseId);
    
    /**
     * 根据次卡订单编号查询次卡优惠券订单映射
     * @param timesCardOrderNo 次卡订单编号
     * @return 次卡优惠券订单映射信息
     */
    TimesCardCouponOrderMappingDO selectByTimesCardOrderNo(@Param("timesCardOrderNo") String timesCardOrderNo);
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 次卡优惠券订单映射信息
     */
    TimesCardCouponOrderMappingDO selectByPrimaryKey(@Param("id") Long id);
    
    /**
     * 插入记录
     * @param record 次卡优惠券订单映射信息
     * @return 影响行数
     */
    int insert(TimesCardCouponOrderMappingDO record);
    
    /**
     * 更新记录
     * @param record 次卡优惠券订单映射信息
     * @return 影响行数
     */
    int updateByPrimaryKey(TimesCardCouponOrderMappingDO record);
    
    /**
     * 删除记录
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("id") Long id);
}
