package so.dian.huashan.common.mapper.lhc;

import org.apache.ibatis.annotations.Param;
import so.dian.huashan.common.mapper.lhc.entity.OrdersBoxDO;

import java.util.List;

/**
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @create: 2023/10/20 17:18
 * @description:
 */
public interface OrdersBoxMapper {

    List<OrdersBoxDO> selectByOrderNos(@Param("orderNos") List<String> orderNos);

    /**
     * 根据订单号查询租赁订单
     * @param orderNo 订单号
     * @return 租赁订单信息
     */
    OrdersBoxDO selectByOrderNo(@Param("orderNo") String orderNo);
}
